<template>
  <div class="abnormal-deduction-drawer">
    <custom-drawer
      :show.sync="showDrawer"
      :size="size"
      :title="title"
      v-bind="$attrs"
      v-on="$listeners"
      :confirmText="getConfirmButtonText()"
      @confirm="submitHandle"
      @cancel="closeHandle"
      :loading="isLoading"
    >
      <div class="abnormal-deduction-drawer-wrapp">
        <!-- 标签页 -->
        <div class="tab-section">
          <div class="tab-buttons">
            <el-button :class="['tab-btn', { active: activeTab === 'scan' }]" @click="clickActiveTab('scan')">
              扣款
            </el-button>
            <el-button
              :class="['tab-btn', { active: activeTab === 'searchOrder' }]"
              @click="clickActiveTab('searchOrder')"
            >
              订单匹配
            </el-button>
            <el-button
              :class="['tab-btn', { active: activeTab === 'searchUser' }]"
              @click="clickActiveTab('searchUser')"
            >
              查找用户
            </el-button>
            <el-button :class="['tab-btn', { active: activeTab === 'faceCheck' }]" @click="clickActiveTab('faceCheck')">
              人脸校验
            </el-button>
          </div>
        </div>

        <!-- 搜索结果区域 -->
        <div v-if="activeTab === 'scan'">
          <div v-if="!Object.keys(scanOrderUserInfo).length" class="no-search-results">
            暂无搜索结果
          </div>
          <div class="search-results-section" v-else>
            <div class="section-title">匹配结果</div>

            <div class="user-item">
              <div class="user-avatar">
                <img v-if="scanOrderUserInfo.face_url" :src="scanOrderUserInfo.face_url" />
                  <div v-else class="selection-avatar-placeholder">暂无人脸</div>
              </div>
              <div class="user-info">
                <div class="user-name">{{ scanOrderUserInfo.name }} ({{ scanOrderUserInfo.person_no }})</div>
                <div class="user-phone">{{ scanOrderUserInfo.phone }}</div>
                <div class="user-group">{{ scanOrderUserInfo.group_name }}</div>
              </div>
              <div class="user-balance">
                <div class="balance-row">
                  <div class="balance-label">储值余额</div>
                  <div class="balance-value">¥ {{ scanOrderUserInfo.balance | formatMoney }}</div>
                </div>
                <div class="balance-row">
                  <div class="balance-label">补贴余额</div>
                  <div class="balance-value">¥ {{ scanOrderUserInfo.subsidy_balance | formatMoney }}</div>
                </div>
                <div class="balance-row">
                  <div class="balance-label">赠送余额</div>
                  <div class="balance-value">¥ {{ scanOrderUserInfo.complimentary_balance | formatMoney }}</div>
                </div>
              </div>
            </div>
            <div class="section-title">扣款金额</div>

            <el-input placeholder="请输入扣款金额" v-model="searchForm.scanPrice" class="w-300">
              <template slot="append">元</template>
            </el-input>
          </div>
        </div>
        <!-- 查找用户 -->
        <div v-if="activeTab === 'searchUser' || activeTab === 'searchOrder'">
          <!-- 查询条件 -->
          <div class="search-conditions-section">
            <div class="section-title">查询条件</div>
            <el-form
              :model="searchForm"
              label-width="80px"
              size="small"
              class="search-form"
              ref="searchForm"
              :rules="searchFormRules"
            >
              <el-form-item label="动账组织" prop="organization">
                <el-select v-model="searchForm.organization" placeholder="请选择动账组织" class="w-250">
                  <el-option
                    v-for="item in organizationList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="用户姓名" prop="person_name">
                <el-input v-model="searchForm.person_name" class="w-250" maxlength="20" placeholder="请输入"></el-input>
              </el-form-item>

              <el-form-item label="手机号" prop="phone">
                <el-input v-model="searchForm.phone" class="w-250" maxlength="13" placeholder="请输入"></el-input>
              </el-form-item>

              <el-form-item label="人员编号" prop="person_no">
                <el-input v-model="searchForm.person_no" class="w-250" maxlength="20" placeholder="请输入"></el-input>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" style="width: 100px" :disabled="!searchForm.person_name && !searchForm.phone && !searchForm.person_no" @click="searchUsers">查询</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 搜索结果 -->
          <div class="search-results-section">
            <div class="section-title">搜索结果</div>
            <div class="user-list-container" v-if="activeTab === 'searchUser'">
              <!-- 暂无搜索结果 -->
              <div v-if="searchResults.length === 0" class="no-search-results">暂无搜索结果</div>
              <!-- 搜索结果列表 -->
              <div
                v-for="user in searchResults"
                :key="user.id"
                class="user-item"
                :class="{ selected: selectedSearchUser && selectedSearchUser.id === user.id }"
                @click="selectSearchUser(user)"
              >
                <div class="user-avatar">
                  <img  v-if="user.face_url" :src="user.face_url" />
                  <div v-else class="selection-avatar-placeholder">暂无人脸</div>
                </div>
                <div class="user-info">
                  <div class="user-name">{{ user.name }} ({{ user.person_no }})</div>
                  <div class="user-phone">{{ user.phone }}</div>
                  <div class="user-group">{{ user.group_name }}</div>
                </div>
                <div class="user-balance">
                  <div class="balance-row">
                    <div class="balance-label">储值余额</div>
                    <div class="balance-value">¥ {{ user.balance | formatMoney }}</div>
                  </div>
                  <div class="balance-row">
                    <div class="balance-label">补贴余额</div>
                    <div class="balance-value">¥ {{ user.subsidy_balance | formatMoney }}</div>
                  </div>
                  <div class="balance-row">
                    <div class="balance-label">赠送余额</div>
                    <div class="balance-value">¥ {{ user.complimentary_balance | formatMoney }}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 订单匹配 -->
            <div class="user-list-container" v-if="activeTab === 'searchOrder'">
              <!-- 暂无搜索结果 -->
              <div v-if="searchOrderResults.length === 0" class="no-search-results">暂无搜索结果</div>
              <!-- 搜索结果列表 -->
              <div
                v-for="orderItem in searchOrderResults"
                :key="orderItem.id"
                class="user-item order-item"
                :class="{ selected: isOrderSelected(orderItem) }"
                @click="toggleOrderSelection(orderItem)"
              >
                <div class="order-checkbox">
                  <el-checkbox
                    :value="isOrderSelected(orderItem)"
                    @change="toggleOrderSelection(orderItem)"
                    @click.stop
                  ></el-checkbox>
                </div>
                <div class="user-order">
                  <div>订单号：{{ orderItem.trade_no }}</div>
                  <div>取餐状态：{{ takeMealStatusName(orderItem.take_meal_status) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 人脸校验 -->
        <div v-if="activeTab === 'faceCheck'">
          <!-- 拍摄人脸区域 -->
          <div v-if="dialogInfo.face">
            <div class="face-capture-section">
              <div class="capture-title">抓拍人脸</div>
              <div class="face-capture-area">
                <div class="face-image-container" v-if="dialogInfo.face">
                  <img :src="dialogInfo.face" class="captured-face-img" />
                </div>
                <el-button type="primary" class="capture-btn" @click="captureFace">在线校验</el-button>
              </div>
            </div>

            <!-- 当前选择区域 -->
            <div class="current-selection-section">
              <div class="selection-title">当前选择</div>
              <div class="selection-table" v-if="Object.keys(selectedFaceUser).length">
                <table class="selection-table-content">
                  <tr>
                    <td class="selection-avatar-cell">
                      <img  v-if="selectedFaceUser.face_url" :src="selectedFaceUser.face_url" class="selection-avatar" />
                      <div v-else class="selection-avatar-placeholder">暂无人脸</div>
                    </td>
                    <td class="selection-name">{{ selectedFaceUser.name }}</td>
                    <td class="selection-id">{{ selectedFaceUser.person_no }}</td>
                    <td class="selection-phone">{{ selectedFaceUser.phone }}</td>
                    <td class="selection-score">{{ selectedFaceUser.score }}</td>
                  </tr>
                </table>
              </div>
              <div v-else class="no-selection-placeholder">当前订单未匹配人脸</div>
            </div>

            <!-- 校验结果表格 -->
            <div class="verification-results-section">
              <div class="results-title">校验结果</div>
              <el-table
                :data="verificationResults"
                style="width: 100%"
                stripe
                header-row-class-name="ps-table-header-row"
                max-height="400"
                border
              >
                <el-table-column type="index" label="排序" width="80" align="center"></el-table-column>
                <el-table-column prop="face_url" label="近似人脸" width="100" align="center">
                  <template slot-scope="scope">
                    <img v-if="scope.row.face_url" :src="scope.row.face_url" class="table-face-img" />
                    <div v-else class="selection-avatar-placeholder">暂无人脸</div>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="姓名" align="center"></el-table-column>
                <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
                <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
                <el-table-column prop="score" label="识别分数" align="center"></el-table-column>
                <el-table-column label="操作" width="100" align="center">
                  <template slot-scope="scope">
                    <el-button
                      type="text"
                      size="small"
                      :disabled="scope.row.user_id === selectedFaceUser.user_id"
                      @click="selectFaceUser(scope.row)"
                    >
                      选择
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div v-else class="no-face-capture">当前订单无抓拍人脸</div>
        </div>
      </div>
    </custom-drawer>
    <verification-deduction-dialog
      :isshow.sync="showDeductionDialog"
      :dialogInfo="userDeductionInfo"
      @comfirmDeduction="comfirmDeduction"
    ></verification-deduction-dialog>
  </div>
</template>

<script>
// import OrganizationSelect from '@/components/OrganizationSelect'
import { mapGetters } from 'vuex'
import VerificationDeductionDialog from './verificationDeductionDialog'
export default {
  name: 'OfflineVerificationDeductionDrawer',
  props: {
    isshow: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '处理'
    },
    size: {
      type: String,
      default: '700px'
    },
    dialogInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    drawerTabs: {
      type: String,
      default: ''
    }
  },
  components: {
    VerificationDeductionDialog
  },
  data() {
    return {
      isLoading: false,
      confirmText: '确认',
      activeTab: 'searchUser',
      searchForm: {
        organization: '',
        person_name: '',
        phone: '',
        person_no: '',
        scanPrice: ''
      },
      searchFormRules: {
        organization: [{ required: true, message: '请选择动账组织', trigger: 'change' }],
        person_name: [{ validator: this.validateAtLeastOne, trigger: 'blur' }],
        phone: [{ validator: this.validateAtLeastOne, trigger: 'blur' }],
        person_no: [{ validator: this.validateAtLeastOne, trigger: 'blur' }]
      },
      organizationList: [],
      selectedSearchUser: {},
      selectedSearchOrder: {}, // 新增：存储选中的订单数据
      selectedSearchOrders: [], // 新增：存储多选的订单数据
      searchResults: [], // 默认为空数组，显示"暂无搜索结果"
      searchOrderResults: [], // 订单匹配
      selectedFaceUser: {},
      verificationResults: [],
      priceReg: /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/,
      showDeductionDialog: false,
      userDeductionInfo: {},
      scanOrderUserInfo: {} // 扣款的用户信息
    }
  },
  computed: {
    showDrawer: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    },
    ...mapGetters(['userInfo'])
  },
  // watch: {
  //   isshow: {
  //     handler(newVal) {
  //       if (newVal) {
  //         // this.organizationList = this.userInfo.organizationList
  //       }
  //     },
  //     immediate: true
  //   }
  // },
  created() {
    this.activeTab = this.drawerTabs
    this.organizationList = this.userInfo.organizationList
  },
  mounted() {},
  methods: {
    // 自定义验证器：至少填写一个字段（用户姓名、手机号、人员编号）
    // eslint-disable-next-line no-unused-vars
    validateAtLeastOne(rule, value, callback) {
      // eslint-disable-next-line camelcase
      const { person_name, phone, person_no } = this.searchForm
      // eslint-disable-next-line camelcase
      if (!person_name && !phone && !person_no) {
        callback(new Error('请至少填写用户姓名、手机号或人员编号中的一项'))
      } else {
        callback()
      }
    },
    takeMealStatusName(type) {
      let takeMealStatus = {
        take_out: '已取餐',
        no_take: '未取餐',
        cancel: '已取消',
        time_out: '已过期'
      }

      return takeMealStatus[type] || ''
    },
    clickActiveTab(type) {
      this.activeTab = type
      if (type === 'scan') {
        let params = {
          take_time: this.dialogInfo.take_time
        }
        if (this.dialogInfo.card_info_id) {
          params.card_info_id = this.dialogInfo.card_info_id
        }
        if (this.dialogInfo.user_card) {
          params.user_card = this.dialogInfo.user_card
        }
        this.getOrderUserInfo(params)
      }
    },

    // 查找用户
    async getOperationData() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOrderOrderOfflineGetOrderUserInfoPost({
          org_ids: [this.searchForm.organization],
          person_name: this.searchForm.person_name,
          phone: this.searchForm.phone,
          person_no: this.searchForm.person_no
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 清空选择数据
        this.selectedSearchUser = {}
        this.selectedSearchOrder = {}
        this.selectedSearchOrders = []
        this.searchResults = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 订单匹配的列表
    async getOrderUser(params) {
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderVerificationAbnormalGetOrderUserPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data && Object.keys(res.data).length) {
          this.searchOrderResults = [...res.data.order_report_meal, ...res.data.order_reservation]
        }
        // 清空选择数据
        this.selectedSearchUser = {}
        this.selectedSearchOrder = {}
        this.selectedSearchOrders = []
      } else {
        this.$message.error(res.msg)
      }
    },
    // 扣款的查找用户
    async getOrderUserInfo(params) {
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderOfflineGetOrderUserInfoPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data && res.data.length) {
          this.scanOrderUserInfo = res.data[0]
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    async getOrderFace() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOrderOrderOfflineGetOrderFacePost({
          company_id: this.dialogInfo.company_id,
          face_url: this.dialogInfo.face,
          count: 10
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 清空选择数据
        this.selectedFaceUser = {}
        this.verificationResults = res.data.map(item => {
          item.score = item.score.toFixed(2)
          return item
        })
      } else {
        this.$message.error(res.msg)
      }
    },

    closeHandle() {
      this.showDrawer = false
    },
    submitHandle() {
      if (this.activeTab === 'faceCheck') {
        if (!this.dialogInfo.face) return this.$message.error('暂无人脸')
        if (!Object.keys(this.selectedFaceUser).length) return this.$message.error('请选择用户')
        this.userDeductionInfo = this.selectedFaceUser
        this.showDeductionDialog = true
        // 人脸校验
        this.setOrdervaBindUser(this.selectedFaceUser.user_id)
      }
      if (this.activeTab === 'searchUser') {
        this.userDeductionInfo = this.selectedSearchUser
        // 查找用户
        if (!Object.keys(this.selectedSearchUser).length) return this.$message.error('请选择用户')
        console.log('提交用户数据:', this.selectedSearchUser)
        this.setOrdervaBindUser(this.selectedSearchUser.id)
      }
      if (this.activeTab === 'searchOrder') {
        // 订单匹配
        if (this.selectedSearchOrders.length === 0) return this.$message.error('请选择订单')

        if (this.selectedSearchOrders.length === 1) {
          // 单选：使用原来的接口
          console.log('提交单个订单数据:', this.selectedSearchOrders[0])
          this.setOrderBindUserInfo(this.selectedSearchOrders[0].order_payment_id)
        } else {
          // 多选：使用新的批量接口
          console.log('提交多个订单数据:', this.selectedSearchOrders)
          const orderPaymentIds = this.selectedSearchOrders.map(order => order.order_payment_id)
          this.setOrderUserList(orderPaymentIds)
        }
      }
      if (this.activeTab === 'scan') {
        if (!Object.keys(this.scanOrderUserInfo).length) return this.$message.error('暂无用户')
        // 限制2位浮点数
        if (!this.priceReg.test(this.searchForm.scanPrice) || Number(this.searchForm.scanPrice) <= 0) {
          this.$message.error('请输入正确的金额格式')
          return
        }
        this.setOrdervaBindUser(this.scanOrderUserInfo.id)
      }
    },
    // 订单匹配信息绑定（单个）
    async setOrderBindUserInfo(id) {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOrderOrderVerificationAbnormalSetOrderUserPost({
          verification_abnormal_id: this.dialogInfo.id,
          order_payment_id: id
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('订单匹配成功')
        this.showDrawer = false
        this.$emit('confirm', 'drawer')
      } else {
        this.$message.error(res.msg)
      }
    },

    // 批量订单匹配信息绑定（多个）
    async setOrderUserList(orderPaymentIds) {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiSetOrderUserList({
          verification_abnormal_id: this.dialogInfo.id,
          order_payment_id_list: orderPaymentIds
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(`成功匹配 ${orderPaymentIds.length} 个订单`)
        this.showDrawer = false
        this.$emit('confirm', 'drawer')
      } else {
        this.$message.error(res.msg)
      }
    },

    // 查找用户，人脸校验绑定
    async setOrdervaBindUser(id) {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOrderOrderVerificationAbnormalSetOrdervaBindUserPost({
          verification_abnormal_id: this.dialogInfo.id,
          card_info_id: id
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
      } else if (res.code === 1) {
        this.showDeductionDialog = true
        this.userDeductionInfo = {
          org_ids: [this.searchForm.organization],
          meal_type: res.data[0],
          msg: res.msg,
          verification_abnormal_id: this.dialogInfo.id,
          card_info_id: id,
          activeTab: this.activeTab, // 目前传这个 为了判断扣款的时候 不弹补扣的弹窗
          origin_fee: this.searchForm.scanPrice || 0 // 只有扣款的时候用到这个金额
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 拍摄人脸
    captureFace() {
      this.getOrderFace()
    },
    // 选择用户
    selectFaceUser(user) {
      this.selectedFaceUser = user
      this.$message.success(`已选择用户：${user.name}`)
    },
    // 搜索用户
    searchUsers() {
      // 先检查至少有一个字段填写了值
      // eslint-disable-next-line camelcase
      const { person_name, phone, person_no } = this.searchForm
      // eslint-disable-next-line camelcase
      if (!person_name && !phone && !person_no) {
        this.$message.error('请至少填写用户姓名、手机号或人员编号中的一项')
        return
      }
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          console.log(this.activeTab)
          if (this.activeTab === 'searchOrder') {
            let params = {
              take_time: this.dialogInfo.take_time,
              org_ids: [this.searchForm.organization],
              person_name: this.searchForm.person_name,
              phone: this.searchForm.phone,
              person_no: this.searchForm.person_no
            }
            if (this.dialogInfo.card_info_id) {
              params.card_info_id = this.dialogInfo.card_info_id
            }
            if (this.dialogInfo.user_card) {
              params.user_card = this.dialogInfo.user_card
            }
            this.getOrderUser(params)
          } else if (this.activeTab === 'searchUser') {
            this.getOperationData()
          }
        }
      })
    },
    // 选择搜索结果中的用户
    selectSearchUser(user) {
      // 根据当前活跃的标签页区分数据类型
      if (this.activeTab === 'searchUser') {
        // 查找用户标签页 - 处理用户数据
        this.selectedSearchUser = user
        this.selectedSearchOrder = {} // 清空订单选择
        this.selectedSearchOrders = [] // 清空多选订单
        this.$message.success(`已选择用户：${user.name}`)
      }
    },
    // 检查订单是否被选中
    isOrderSelected(order) {
      return this.selectedSearchOrders.some(item => item.id === order.id)
    },
    // 切换订单选择状态
    toggleOrderSelection(order) {
      const index = this.selectedSearchOrders.findIndex(item => item.id === order.id)
      if (index > -1) {
        // 如果已选中，则取消选择
        this.selectedSearchOrders.splice(index, 1)
      } else {
        // 如果未选中，则添加到选择列表
        this.selectedSearchOrders.push(order)
      }

      // 更新单选数据（保持兼容性）
      if (this.selectedSearchOrders.length === 1) {
        this.selectedSearchOrder = this.selectedSearchOrders[0]
      } else {
        this.selectedSearchOrder = {}
      }

      // 清空用户选择
      this.selectedSearchUser = {}

      const selectedCount = this.selectedSearchOrders.length
      if (selectedCount > 0) {
        this.$message.success(`已选择 ${selectedCount} 个订单`)
      }
    },
    // 扣款成功弹窗返回
    comfirmDeduction() {
      this.showDrawer = false
      this.$emit('confirm', 'drawer')
    }
  }
}
</script>

<style scoped lang="scss">
.abnormal-deduction-drawer {
  .abnormal-deduction-drawer-wrapp {
    padding: 0 20px;

    .drawer-header {
      margin-bottom: 15px;

      .header-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .tab-section {
      margin-bottom: 20px;

      .tab-buttons {
        display: flex;
        gap: 0;

        .tab-btn {
          border-radius: 0;
          border: 1px solid #ff9b45;
          background: #fff;
          color: #ff9b45;
          padding: 8px 20px;
          font-size: 14px;
          margin-right: -1px;
          border-radius: 4px;

          &:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
          }

          &:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
          }

          &.active {
            background: #ff9b45;
            color: #fff;
            border-color: #ff9b45;
            z-index: 1;
          }
        }
      }
    }

    .payment-method-section {
      margin-bottom: 20px;

      .section-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 500;
      }
    }

    // 查找用户相关样式
    .search-conditions-section {
      margin-bottom: 20px;

      .section-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 500;
      }
    }

    .search-results-section {
      margin-bottom: 20px;

      .section-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 500;
      }

      .user-item {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: all 0.3s;
        background: #fff;

        &:hover {
          border-color: #ff9b45;
          background-color: #fff9f5;
        }

        &.selected {
          border-color: #ff9b45;
          background-color: #fff9f5;
        }

        .user-avatar {
          flex-shrink: 0;

          img {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            object-fit: cover;
          }
        }

        .user-info {
          flex: 1;

          .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
          }

          .user-phone {
            font-size: 13px;
            color: #666;
            margin-bottom: 5px;
          }

          .user-group {
            font-size: 13px;
            color: #666;
          }
        }
        .user-order {
          display: flex;
          justify-content: space-between;
          padding: 0 10px;
          width: 100%;
        }

        &.order-item {
          display: flex;
          align-items: center;
          gap: 15px;

          .order-checkbox {
            flex-shrink: 0;
            display: flex;
            align-items: center;
          }

          .user-order {
            flex: 1;
            padding: 0;
          }
        }

        .user-balance {
          display: flex;
          align-items: center;
          justify-content: center;

          .balance-row {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 5px;
            min-width: 120px;

            .balance-label {
              padding-bottom: 20px;
              font-size: 13px;
              color: #666;
            }

            .balance-value {
              font-size: 13px;
              color: #333;
              font-weight: 500;
            }
          }
        }
      }

      .user-list-container {
        max-height: 290px;
        overflow-y: auto;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 10px;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }

      .no-search-results {
        padding: 40px 20px;
        text-align: center;
        color: #909399;
        background-color: #f5f7fa;
        border-radius: 4px;
        font-size: 14px;
      }
      // }
    }
    // 人脸校验相关样式
    .face-capture-section {
      margin-bottom: 20px;

      .capture-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 500;
      }

      .face-capture-area {
        display: flex;
        align-items: flex-end;
        gap: 15px;

        .face-image-container {
          width: 150px;
          height: 150px;
          border-radius: 4px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f7fa;

          .captured-face-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .capture-btn {
          color: #fff;
          border: none;
          padding: 8px 15px;
          border-radius: 4px;
          width: 100px;
        }
      }
    }
    .no-face-capture {
      height: 100px;
      text-align: center;
      background: #e7e9ee;
      line-height: 100px;
      margin-bottom: 20px;
    }
    .current-selection-section {
      margin-bottom: 20px;

      .selection-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 500;
      }

      .selection-table {
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;

        .selection-table-content {
          width: 100%;
          border-collapse: collapse;

          tr {
            td {
              padding: 12px;
              border-right: 1px solid #ddd;
              text-align: center;
              font-size: 14px;
              color: #333;

              &:last-child {
                border-right: none;
              }

              &.selection-label {
                background-color: #f5f7fa;
                font-weight: 500;
                width: 100px;
              }

              &.selection-avatar-cell {
                width: 100px;
                padding: 8px;

                .selection-avatar {
                  width: 60px;
                  height: 60px;
                  border-radius: 4px;
                  object-fit: cover;
                }
              }

              &.selection-name {
                font-weight: 500;
                width: 25%;
              }

              &.selection-id {
                color: #666;
                width: 25%;
              }

              &.selection-phone {
                color: #666;
                width: 30%;
              }

              &.selection-score {
                color: #666;
                width: 20%;
              }
            }
          }
        }
      }

      .no-selection-placeholder {
        padding: 20px;
        text-align: center;
        color: #909399;
        background-color: #f5f7fa;
        border-radius: 4px;
      }
    }

    .verification-results-section {
      margin-bottom: 20px;

      .results-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 500;
      }

      .table-face-img {
        width: 60px;
        height: 60px;
        border-radius: 4px;
        object-fit: cover;
      }
    }
  }
  .selection-avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #909399;
    text-align: center;
  }
}
</style>

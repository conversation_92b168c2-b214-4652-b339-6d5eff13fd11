import http from '@/utils/request'
export default {
  /**
   * 线下核销订单列表
   * @param {*} params
   * @returns
   */
  apiBackgroundOrderOrderVerificationAbnormalListPost (params) {
    return http.post('/api/background_order/order_verification_abnormal/list', params)
  },
  /**
   * 线下核销订单合计
   * @param {*} params
   * @returns
   */
  apiBackgroundOrderOrderVerificationAbnormalListCollectPost (params) {
    return http.post('/api/background_order/order_verification_abnormal/list_collect', params)
  },
  /**
   * 线下核销订单列表导出
   * @param {*} params
   * @returns
   */
  apiBackgroundOrderOrderVerificationAbnormalListExportPost (params) {
    return http.post('/api/background_order/order_verification_abnormal/list_export', params)
  },
  /**
   * 离线订单关闭
   * @param {*} params
   * @returns
   */
  apiBackgroundOrderOrderVerificationAbnormalOrderClosePost (params) {
    return http.post('/api/background_order/order_verification_abnormal/order_close', params)
  },
  /**
   * 批量设置订单用户
   * @param {*} params
   * @returns
   */
  apiSetOrderUserList (params) {
    return http.post('/api/background_order/order_verification_abnormal/set_order_user_list', params)
  }
}
